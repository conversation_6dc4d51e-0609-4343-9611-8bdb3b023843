use terminus_ui::prelude::*;

/// Demo 1: Basic vertical and horizontal scrolling side by side
fn main() -> Result<(), Box<dyn std::error::Error>> {
    let element = rsx! {
        <Layout direction={Direction::Vertical} constraints={vec![
            Constraint::Length(3),  // Header
            Constraint::Min(0),     // Content
            Constraint::Length(3),  // Footer
        ]}>
            // Header
            <Block
                title="Demo 1: Basic Scrolling"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Text content="Vertical and Horizontal ScrollArea Demo" />
            </Block>

            // Main content area
            <Layout direction={Direction::Horizontal} constraints={vec![
                Constraint::Percentage(50),
                Constraint::Percentage(50),
            ]}>
                // Vertical scrolling demo
                <ScrollArea
                    title={Some("Vertical Scrolling".to_string())}
                    orientation={ScrollOrientation::Vertical}
                    show_scrollbars={true}
                    borders={Some(Borders::ALL)}
                    border_style={Some(Style::default().fg(Color::Green))}
                    scroll_step={1}
                >
                    {generate_long_text_elements("Vertical Content", 50)}
                </ScrollArea>

                // Horizontal scrolling demo
                <ScrollArea
                    title={Some("Horizontal Scrolling".to_string())}
                    orientation={ScrollOrientation::Horizontal}
                    show_scrollbars={true}
                    borders={Some(Borders::ALL)}
                    border_style={Some(Style::default().fg(Color::Magenta))}
                    scroll_step={2}
                >
                    <Text content="Horizontal Content line 1 - This is a very long line that should extend beyond the normal terminal width to demonstrate horizontal scrolling capabilities in the ScrollArea component with smooth navigation and professional TUI design patterns." />
                    <Text content="Horizontal Content line 2 - This is a very long line that should extend beyond the normal terminal width to demonstrate horizontal scrolling capabilities in the ScrollArea component with smooth navigation and professional TUI design patterns." />
                    <Text content="Horizontal Content line 3 - This is a very long line that should extend beyond the normal terminal width to demonstrate horizontal scrolling capabilities in the ScrollArea component with smooth navigation and professional TUI design patterns." />
                    <Text content="Horizontal Content line 4 - This is a very long line that should extend beyond the normal terminal width to demonstrate horizontal scrolling capabilities in the ScrollArea component with smooth navigation and professional TUI design patterns." />
                    <Text content="Horizontal Content line 5 - This is a very long line that should extend beyond the normal terminal width to demonstrate horizontal scrolling capabilities in the ScrollArea component with smooth navigation and professional TUI design patterns." />
                    <Text content="Horizontal Content line 6 - This is a very long line that should extend beyond the normal terminal width to demonstrate horizontal scrolling capabilities in the ScrollArea component with smooth navigation and professional TUI design patterns." />
                    <Text content="Horizontal Content line 7 - This is a very long line that should extend beyond the normal terminal width to demonstrate horizontal scrolling capabilities in the ScrollArea component with smooth navigation and professional TUI design patterns." />
                    <Text content="Horizontal Content line 8 - This is a very long line that should extend beyond the normal terminal width to demonstrate horizontal scrolling capabilities in the ScrollArea component with smooth navigation and professional TUI design patterns." />
                    <Text content="Horizontal Content line 9 - This is a very long line that should extend beyond the normal terminal width to demonstrate horizontal scrolling capabilities in the ScrollArea component with smooth navigation and professional TUI design patterns." />
                    <Text content="Horizontal Content line 10 - This is a very long line that should extend beyond the normal terminal width to demonstrate horizontal scrolling capabilities in the ScrollArea component with smooth navigation and professional TUI design patterns." />
                </ScrollArea>
            </Layout>

            // Footer with instructions
            <Block
                title="Controls"
                borders={Borders::TOP}
                border_style={Style::default().fg(Color::Yellow)}
            >
                <Text content="↑/↓/←/→ = Scroll, Page Up/Down = Fast scroll, Q/Esc = Quit" />
            </Block>
        </Layout>
    };

    render(element)
}

/// Generate long text elements for vertical scrolling demos
fn generate_long_text_elements(prefix: &str, lines: usize) -> Vec<Element> {
    (1..=lines)
        .map(|i| {
            rsx! {
                <Text content={format!(
                    "{} line {} - Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
                    prefix, i
                )} />
            }
        })
        .collect()
}
