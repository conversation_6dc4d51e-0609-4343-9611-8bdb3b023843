use terminus_ui::prelude::*;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let element = rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={vec![
                Constraint::Length(3),  // Header
                Constraint::Min(0),     // Main content with ScrollArea
                Constraint::Length(3),  // Footer
            ]}
        >
            // Header
            <Block
                title="ScrollArea Demo"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Text content="Terminus UI - ScrollArea Component Demo" />
            </Block>

            // Main content area with ScrollArea
            <Layout
                direction={Direction::Horizontal}
                constraints={vec![
                    Constraint::Percentage(50),  // Left ScrollArea
                    Constraint::Percentage(50),  // Right ScrollArea
                ]}
            >
                // Vertical ScrollArea
                <ScrollArea
                    title={Some("Vertical Scroll".to_string())}
                    orientation={ScrollOrientation::Vertical}
                    show_scrollbars={true}
                    scroll_step={1}
                    borders={Some(Borders::ALL)}
                    border_style={Some(Style::default().fg(Color::Green))}
                >
                    <Text content="Line 1: This is a long scrollable content area" />
                    <Text content="Line 2: Use arrow keys or j/k to scroll vertically" />
                    <Text content="Line 3: The ScrollArea component manages overflow" />
                    <Text content="Line 4: Content that exceeds the viewport height" />
                    <Text content="Line 5: Will be scrollable with smooth navigation" />
                    <Text content="Line 6: PageUp/PageDown for faster scrolling" />
                    <Text content="Line 7: Following Shadcn UI design patterns" />
                    <Text content="Line 8: Professional TUI aesthetics" />
                    <Text content="Line 9: Type-safe component props" />
                    <Text content="Line 10: React-like component architecture" />
                    <Text content="Line 11: Scrollbars provide visual feedback" />
                    <Text content="Line 12: Vertical scrollbar shows position" />
                    <Text content="Line 13: Horizontal scrollbar for wide content" />
                    <Text content="Line 14: Smooth scrolling with keyboard controls" />
                    <Text content="Line 15: Bounded scrolling prevents overflow" />
                    <Text content="Line 16: Content height calculation is dynamic" />
                    <Text content="Line 17: Viewport height adapts to container" />
                    <Text content="Line 18: Scroll step controls movement speed" />
                    <Text content="Line 19: Multiple scroll orientations supported" />
                    <Text content="Line 20: Both vertical and horizontal modes" />
                    <Text content="Line 21: Customizable scrollbar symbols" />
                    <Text content="Line 22: Track and thumb visual indicators" />
                    <Text content="Line 23: Begin and end symbol customization" />
                    <Text content="Line 24: Style properties for theming" />
                    <Text content="Line 25: Border and title support" />
                    <Text content="Line 26: Integration with layout system" />
                    <Text content="Line 27: Event handling for scroll callbacks" />
                    <Text content="Line 28: State management with hooks" />
                    <Text content="Line 29: Performance optimized rendering" />
                    <Text content="Line 30: Memory efficient content handling" />
                    <Text content="Line 31: Cross-platform terminal support" />
                    <Text content="Line 32: Unicode character compatibility" />
                    <Text content="Line 33: Color and styling flexibility" />
                    <Text content="Line 34: Responsive design principles" />
                    <Text content="Line 35: Accessibility considerations" />
                    <Text content="Line 36: Developer-friendly API design" />
                    <Text content="Line 37: Comprehensive error handling" />
                    <Text content="Line 38: Documentation and examples" />
                    <Text content="Line 39: Testing and quality assurance" />
                    <Text content="Line 40: Community feedback integration" />
                    <Text content="Line 41: Continuous improvement process" />
                    <Text content="Line 42: Modern Rust development practices" />
                    <Text content="Line 43: Type safety and memory safety" />
                    <Text content="Line 44: Zero-cost abstractions where possible" />
                    <Text content="Line 45: Efficient terminal I/O operations" />
                    <Text content="Line 46: Minimal resource consumption" />
                    <Text content="Line 47: Fast startup and response times" />
                    <Text content="Line 48: Scalable architecture design" />
                    <Text content="Line 49: Modular component system" />
                    <Text content="Line 50: End of scrollable content area" />
                </ScrollArea>

                // Horizontal ScrollArea
                <ScrollArea
                    title={Some("Horizontal Scroll".to_string())}
                    orientation={ScrollOrientation::Horizontal}
                    show_scrollbars={true}
                    scroll_step={2}
                    borders={Some(Borders::ALL)}
                    border_style={Some(Style::default().fg(Color::Magenta))}
                >
                    <Text content="This is a very long line of text that should exceed the viewport width and require horizontal scrolling to view completely. Use arrow keys or h/l to scroll horizontally." />
                    <Text content="Another long line demonstrating horizontal scrolling capabilities with smooth navigation and professional TUI design patterns." />
                    <Text content="The ScrollArea component supports both vertical and horizontal scrolling with customizable scroll steps and visual indicators." />
                </ScrollArea>
            </Layout>

            // Footer with instructions
            <Block
                title="Controls"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Yellow)}
            >
                <Text content="↑↓ or j/k: Vertical scroll | ←→ or h/l: Horizontal scroll | PageUp/PageDown: Fast scroll | q/Esc: Quit" />
            </Block>
        </Layout>
    };

    render(element)
}
