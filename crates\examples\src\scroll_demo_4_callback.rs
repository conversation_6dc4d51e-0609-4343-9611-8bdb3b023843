use terminus_ui::prelude::*;

/// Demo 4: ScrollArea with scroll callback demonstration
#[derive(<PERSON><PERSON>, Debu<PERSON>, <PERSON>lone)]
pub struct CallbackDemoProps {
    pub title: String,
}

#[component(CallbackDemo)]
fn callback_demo(props: CallbackDemoProps) -> Element {
    let (scroll_callback_info, set_scroll_callback_info) =
        use_state("No scroll events yet".to_string());

    // Scroll callback
    let on_scroll = {
        let set_info = set_scroll_callback_info.clone();
        move |pos: (usize, usize)| {
            set_info.call(format!("Scroll position: Y={}, X={}", pos.0, pos.1));
        }
    };

    rsx! {
        <Layout direction={Direction::Vertical} constraints={vec![
            Constraint::Length(3),  // Header
            Constraint::Length(3),  // Callback info
            Constraint::Min(0),     // Content
            Constraint::Length(3),  // Footer
        ]}>
            // Header
            <Block 
                title={props.title} 
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Text content="ScrollArea with Scroll Callback Demo" />
            </Block>

            // Callback info
            <Block 
                title="Scroll Callback Info"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::White)}
            >
                <Text content={scroll_callback_info.get()} />
            </Block>

            // Scrollable area with callback
            <ScrollArea
                title={Some("Scroll to see callback in action".to_string())}
                orientation={ScrollOrientation::Vertical}
                show_scrollbars={true}
                borders={Some(Borders::ALL)}
                border_style={Some(Style::default().fg(Color::Cyan))}
                scroll_step={1}
                on_scroll={on_scroll}
            >
                {generate_long_text_elements("Callback Content", 40)}
            </ScrollArea>

            // Footer with instructions
            <Block 
                title="Controls"
                borders={Borders::TOP}
                border_style={Style::default().fg(Color::Yellow)}
            >
                <Text content="↑/↓ = Scroll, Page Up/Down = Fast scroll, Q/Esc = Quit" />
            </Block>
        </Layout>
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let demo_props = CallbackDemoProps {
        title: "Demo 4: Scroll Callback".to_string(),
    };

    let element = rsx! {
        <CallbackDemo title={demo_props.title} />
    };

    render(element)
}

/// Generate long text elements for vertical scrolling demos
fn generate_long_text_elements(prefix: &str, lines: usize) -> Vec<Element> {
    (1..=lines)
        .map(|i| {
            rsx! {
                <Text content={format!(
                    "{} line {} - Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
                    prefix, i
                )} />
            }
        })
        .collect()
}
