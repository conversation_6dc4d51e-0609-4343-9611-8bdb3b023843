use crossterm::event::{Event, KeyCode};
use terminus_ui::prelude::*;

/// Comprehensive demonstration of the ScrollArea component
/// This example shows various scrolling patterns and configurations

#[derive(Props, Debug, Clone)]
pub struct ScrollDemoProps {
    pub title: String,
}

/// Main demo component showcasing different ScrollArea configurations
#[component(ScrollDemo)]
fn scroll_demo(props: ScrollDemoProps) -> Element {
    let (selected_demo, set_selected_demo) = use_state(0usize);
    let (scroll_callback_info, set_scroll_callback_info) =
        use_state("No scroll events yet".to_string());

    // Handle demo navigation
    if let Some(Event::Key(key)) = use_event() {
        if key.is_press() {
            match key.code {
                KeyCode::Tab => {
                    let next_demo = (selected_demo.get() + 1) % 4;
                    set_selected_demo.call(next_demo);
                }
                KeyCode::BackTab => {
                    let prev_demo = if selected_demo.get() == 0 {
                        3
                    } else {
                        selected_demo.get() - 1
                    };
                    set_selected_demo.call(prev_demo);
                }
                _ => {}
            }
        }
    }

    // Scroll callback for demo 4
    let on_scroll = {
        let set_info = set_scroll_callback_info.clone();
        move |pos: (usize, usize)| {
            set_info.call(format!("Scroll position: Y={}, X={}", pos.0, pos.1));
        }
    };

    rsx! {
        <Layout direction={Direction::Vertical} constraints={vec![
            Constraint::Length(3),  // Header
            Constraint::Min(0),     // Content
            Constraint::Length(3),  // Footer
        ]}>
            // Header
            <Block
                title={props.title}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Text content={format!("Demo {}/4 - Use Tab/Shift+Tab to navigate, Q/Esc to quit", selected_demo.get() + 1)} />
            </Block>

            // Main content area
            {match selected_demo.get() {
                0 => rsx! {
                    <Layout direction={Direction::Horizontal} constraints={vec![
                        Constraint::Percentage(50),
                        Constraint::Percentage(50),
                    ]}>
                        // Vertical scrolling demo
                        <ScrollArea
                            title={Some("Vertical Scrolling".to_string())}
                            orientation={ScrollOrientation::Vertical}
                            show_scrollbars={true}
                            borders={Some(Borders::ALL)}
                            border_style={Some(Style::default().fg(Color::Green))}
                            scroll_step={1}
                        >
                            {generate_long_text_elements("Vertical Content", 50)}
                        </ScrollArea>

                        // Horizontal scrolling demo
                        <ScrollArea
                            title={Some("Horizontal Scrolling".to_string())}
                            orientation={ScrollOrientation::Horizontal}
                            show_scrollbars={true}
                            borders={Some(Borders::ALL)}
                            border_style={Some(Style::default().fg(Color::Magenta))}
                            scroll_step={2}
                        >
                            {generate_wide_text_elements("Horizontal Content", 10)}
                        </ScrollArea>
                    </Layout>
                },
                1 => rsx! {
                    <ScrollArea
                        title={Some("Both Directions Scrolling".to_string())}
                        orientation={ScrollOrientation::Both}
                        show_scrollbars={true}
                        borders={Some(Borders::ALL)}
                        border_style={Some(Style::default().fg(Color::Yellow))}
                        scroll_step={1}
                    >
                        {generate_large_text_elements("Both Directions", 30, 20)}
                    </ScrollArea>
                },
                2 => rsx! {
                    <Layout direction={Direction::Horizontal} constraints={vec![
                        Constraint::Percentage(50),
                        Constraint::Percentage(50),
                    ]}>
                        // With scrollbar
                        <ScrollArea
                            title={Some("With Scrollbar".to_string())}
                            orientation={ScrollOrientation::Vertical}
                            show_scrollbars={true}
                            borders={Some(Borders::ALL)}
                            border_style={Some(Style::default().fg(Color::Blue))}
                            scroll_step={2}
                        >
                            {generate_long_text_elements("Scrollbar Visible", 30)}
                        </ScrollArea>

                        // Without scrollbar
                        <ScrollArea
                            title={Some("Without Scrollbar".to_string())}
                            orientation={ScrollOrientation::Vertical}
                            show_scrollbars={false}
                            borders={Some(Borders::ALL)}
                            border_style={Some(Style::default().fg(Color::Red))}
                            scroll_step={1}
                        >
                            {generate_long_text_elements("No Scrollbar", 30)}
                        </ScrollArea>
                    </Layout>
                },
                3 => rsx! {
                    <Layout direction={Direction::Vertical} constraints={vec![
                        Constraint::Length(3),
                        Constraint::Min(0),
                    ]}>
                        // Callback info
                        <Block
                            title={"Scroll Callback Demo".to_string()}
                            borders={Borders::ALL}
                            border_style={Style::default().fg(Color::White)}
                        >
                            <Text content={scroll_callback_info.get()} />
                        </Block>

                        // Scrollable area with callback
                        <ScrollArea
                            title={Some("Scroll to see callback in action".to_string())}
                            orientation={ScrollOrientation::Vertical}
                            show_scrollbars={true}
                            borders={Some(Borders::ALL)}
                            border_style={Some(Style::default().fg(Color::Cyan))}
                            scroll_step={1}
                            on_scroll={on_scroll}
                        >
                            {generate_long_text_elements("Callback Content", 40)}
                        </ScrollArea>
                    </Layout>
                },
                _ => rsx! {
                    <Text content="Invalid demo selection" />
                },
            }}

            // Footer with instructions
            <Block
                title={"Controls".to_string()}
                borders={Borders::TOP}
                border_style={Style::default().fg(Color::Yellow)}
            >
                <Text content="↑/↓/←/→ = Scroll, Page Up/Down = Fast scroll, Home/End = Jump, Tab = Next demo, Q/Esc = Quit" />
            </Block>
        </Layout>
    }
}

/// Generate long text elements for vertical scrolling demos
fn generate_long_text_elements(prefix: &str, lines: usize) -> Vec<Element> {
    (1..=lines)
        .map(|i| {
            rsx! {
                <Text content={format!(
                    "{} line {} - Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
                    prefix, i
                )} />
            }
        })
        .collect()
}

/// Generate wide text elements for horizontal scrolling demos
fn generate_wide_text_elements(prefix: &str, lines: usize) -> Vec<Element> {
    (1..=lines)
        .map(|i| {
            rsx! {
                <Text content={format!("{} line {} - This is a very long line that should extend beyond the normal terminal width to demonstrate horizontal scrolling capabilities in the ScrollArea component with smooth navigation and professional TUI design patterns.", prefix, i)} />
            }
        })
        .collect()
}

/// Generate large text elements for both-directions scrolling
fn generate_large_text_elements(prefix: &str, lines: usize, line_length: usize) -> Vec<Element> {
    (1..=lines)
        .map(|i| {
            let base_text = format!("{} line {} - ", prefix, i);
            let padding = "Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua ";
            let repeated_padding = padding.repeat((line_length * 10) / padding.len() + 1);
            let content = format!("{}{}", base_text, &repeated_padding[..line_length.min(repeated_padding.len())]);

            rsx! {
                <Text content={content} />
            }
        })
        .collect()
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let demo_props = ScrollDemoProps {
        title: "🔄 ScrollArea Component Demo - Terminus UI".to_string(),
    };

    let element = rsx! {
        <ScrollDemo title={demo_props.title} />
    };

    render(element)
}
