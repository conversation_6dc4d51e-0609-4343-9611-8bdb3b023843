use terminus_ui::prelude::*;

/// Demo 3: Comparison between ScrollArea with and without scrollbars
fn main() -> Result<(), Box<dyn std::error::Error>> {
    let element = rsx! {
        <Layout direction={Direction::Vertical} constraints={vec![
            Constraint::Length(3),  // Header
            Constraint::Min(0),     // Content
            Constraint::Length(3),  // Footer
        ]}>
            // Header
            <Block
                title="Demo 3: Scrollbar Comparison"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Text content="ScrollArea with and without scrollbars" />
            </Block>

            // Main content area
            <Layout direction={Direction::Horizontal} constraints={vec![
                Constraint::Percentage(50),
                Constraint::Percentage(50),
            ]}>
                // With scrollbar
                <ScrollArea
                    title={Some("With Scrollbar".to_string())}
                    orientation={ScrollOrientation::Vertical}
                    show_scrollbars={true}
                    borders={Some(Borders::ALL)}
                    border_style={Some(Style::default().fg(Color::Blue))}
                    scroll_step={2}
                >
                    {generate_long_text_elements("Scrollbar Visible", 30)}
                </ScrollArea>

                // Without scrollbar
                <ScrollArea
                    title={Some("Without Scrollbar".to_string())}
                    orientation={ScrollOrientation::Vertical}
                    show_scrollbars={false}
                    borders={Some(Borders::ALL)}
                    border_style={Some(Style::default().fg(Color::Red))}
                    scroll_step={1}
                >
                    {generate_long_text_elements("No Scrollbar", 30)}
                </ScrollArea>
            </Layout>

            // Footer with instructions
            <Block
                title="Controls"
                borders={Borders::TOP}
                border_style={Style::default().fg(Color::Yellow)}
            >
                <Text content="↑/↓ = Scroll, Page Up/Down = Fast scroll, Q/Esc = Quit" />
            </Block>
        </Layout>
    };

    render(element)
}

/// Generate long text elements for vertical scrolling demos
fn generate_long_text_elements(prefix: &str, lines: usize) -> Vec<Element> {
    (1..=lines)
        .map(|i| {
            rsx! {
                <Text content={format!(
                    "{} line {} - Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
                    prefix, i
                )} />
            }
        })
        .collect()
}
