use terminus_ui::prelude::*;

/// Demo 2: Both directions scrolling in a single ScrollArea
fn main() -> Result<(), Box<dyn std::error::Error>> {
    let element = rsx! {
        <Layout direction={Direction::Vertical} constraints={vec![
            Constraint::Length(3),  // Header
            Constraint::Min(0),     // Content
            Constraint::Length(3),  // Footer
        ]}>
            // Header
            <Block
                title="Demo 2: Both Directions Scrolling"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Text content="ScrollArea with Both Vertical and Horizontal Scrolling" />
            </Block>

            // Main content area
            <ScrollArea
                title={Some("Both Directions Scrolling".to_string())}
                orientation={ScrollOrientation::Both}
                show_scrollbars={true}
                borders={Some(Borders::ALL)}
                border_style={Some(Style::default().fg(Color::Yellow))}
                scroll_step={1}
            >
                {generate_large_text_elements("Both Directions", 30, 20)}
            </ScrollArea>

            // Footer with instructions
            <Block
                title="Controls"
                borders={Borders::TOP}
                border_style={Style::default().fg(Color::Yellow)}
            >
                <Text content="↑/↓/←/→ = Scroll, Page Up/Down = Fast scroll, Q/Esc = Quit" />
            </Block>
        </Layout>
    };

    render(element)
}

/// Generate large text elements for both-directions scrolling
fn generate_large_text_elements(prefix: &str, lines: usize, line_length: usize) -> Vec<Element> {
    (1..=lines)
        .map(|i| {
            let base_text = format!("{} line {} - ", prefix, i);
            let padding = "Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua ";
            let repeated_padding = padding.repeat((line_length * 10) / padding.len() + 1);
            let content = format!("{}{}", base_text, &repeated_padding[..line_length.min(repeated_padding.len())]);

            rsx! {
                <Text content={content} />
            }
        })
        .collect()
}
